#include "motor.h"
#include "Track_Config.h"  // 引入配置文件

// 全局PID参数初始化
Motor_PID_t motor_pid = {
    .base_speed = TRACK_BASE_SPEED_NORMAL,
    .max_speed = TRACK_MAX_SPEED,
    .min_speed = TRACK_MIN_SPEED,
    .kp = TRACK_PID_KP,
    .ki = TRACK_PID_KI,
    .kd = TRACK_PID_KD,
    .last_error = 0,
    .integral = 0,
    .max_integral = TRACK_PID_MAX_INTEGRAL
};

void Set_PWM(int pwmA,int pwmB)
{
    // 限制PWM值范围，防止过大的值损坏电机
    if(pwmA > MAX_SPEED) pwmA = MAX_SPEED;
    if(pwmA < -MAX_SPEED) pwmA = -MAX_SPEED;
    if(pwmB > MAX_SPEED) pwmB = MAX_SPEED;
    if(pwmB < -MAX_SPEED) pwmB = -MAX_SPEED;

	 if(pwmA>0)
    {
        DL_GPIO_setPins(AIN_PORT,AIN_AIN2_PIN);
        DL_GPIO_clearPins(AIN_PORT,AIN_AIN1_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmA),GPIO_PWM_0_C0_IDX);
    }
    else
    {
        DL_GPIO_setPins(AIN_PORT,AIN_AIN1_PIN);
        DL_GPIO_clearPins(AIN_PORT,AIN_AIN2_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmA),GPIO_PWM_0_C0_IDX);
    }
    if(pwmB>0)
    {
		DL_GPIO_setPins(BIN_PORT,BIN_BIN2_PIN);
        DL_GPIO_clearPins(BIN_PORT,BIN_BIN1_PIN);
        DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmB),GPIO_PWM_0_C1_IDX);
    }
    else
    {
		DL_GPIO_setPins(BIN_PORT,BIN_BIN1_PIN);
        DL_GPIO_clearPins(BIN_PORT,BIN_BIN2_PIN);
		 DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmB),GPIO_PWM_0_C1_IDX);
    }
}

// 传统控制函数（使用配置参数，保持兼容性）
void Right_Control(void)
{
	Set_PWM(TRACK_RIGHT_SPEED_LOW, TRACK_RIGHT_SPEED_HIGH);
}

void Left_Control(void)
{
	Set_PWM(TRACK_LEFT_SPEED_HIGH, TRACK_LEFT_SPEED_LOW);
}

void Left_Large_Control(void)
{
	Set_PWM(TRACK_LARGE_TURN_FORWARD, -TRACK_LARGE_TURN_REVERSE);
}

void Right_Large_Control(void)
{
	Set_PWM(-TRACK_LARGE_TURN_REVERSE, TRACK_LARGE_TURN_FORWARD);
}

void Left_Little_Control(void)
{
	Set_PWM(TRACK_LITTLE_TURN_HIGH, TRACK_LITTLE_TURN_LOW);
}

void Right_Little_Control(void)
{
	Set_PWM(TRACK_LITTLE_TURN_LOW, TRACK_LITTLE_TURN_HIGH);
}

// 新增：平滑控制函数
void Motor_Smooth_Control(int error, int base_speed)
{
    int left_speed, right_speed;
    int turn_adjustment;

    // 根据误差计算转向调整量（使用配置参数）
    turn_adjustment = error * TRACK_SMOOTH_FACTOR;

    // 限制转向调整量
    if(turn_adjustment > TRACK_MAX_TURN_DIFF) turn_adjustment = TRACK_MAX_TURN_DIFF;
    if(turn_adjustment < -TRACK_MAX_TURN_DIFF) turn_adjustment = -TRACK_MAX_TURN_DIFF;

    // 计算左右轮速度
    left_speed = base_speed - turn_adjustment;
    right_speed = base_speed + turn_adjustment;

    // 确保速度在合理范围内
    if(left_speed < TRACK_MIN_SPEED && left_speed > 0) left_speed = TRACK_MIN_SPEED;
    if(right_speed < TRACK_MIN_SPEED && right_speed > 0) right_speed = TRACK_MIN_SPEED;
    if(left_speed > TRACK_MAX_SPEED) left_speed = TRACK_MAX_SPEED;
    if(right_speed > TRACK_MAX_SPEED) right_speed = TRACK_MAX_SPEED;

    Set_PWM(left_speed, right_speed);
}

// 新增：PID控制函数
void Motor_PID_Control(int error, int base_speed)
{
    int derivative, output;

    // 积分计算（带限幅）
    motor_pid.integral += error;
    if(motor_pid.integral > motor_pid.max_integral)
        motor_pid.integral = motor_pid.max_integral;
    if(motor_pid.integral < -motor_pid.max_integral)
        motor_pid.integral = -motor_pid.max_integral;

    // 微分计算
    derivative = error - motor_pid.last_error;

    // PID输出计算
    output = (int)(motor_pid.kp * error +
                   motor_pid.ki * motor_pid.integral +
                   motor_pid.kd * derivative);

    // 更新上次误差
    motor_pid.last_error = error;

    // 使用PID输出进行电机控制
    Motor_Smooth_Control(output, base_speed);
}

// 新增：停止电机函数
void Motor_Stop(void)
{
    Set_PWM(0, 0);
}
