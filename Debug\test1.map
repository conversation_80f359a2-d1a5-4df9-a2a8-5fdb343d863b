******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 17:14:29 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000033a9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005738  0001a8c8  R  X
  SRAM                  20200000   00008000  000009b7  00007649  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005738   00005738    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003b80   00003b80    r-x .text
  00003c40    00003c40    00001a80   00001a80    r-- .rodata
  000056c0    000056c0    00000078   00000078    r-- .cinit
20200000    20200000    000007ba   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000025a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003b80     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000016c     motor.o (.text.Set_PWM)
                  000008ac    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000a08    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000b5c    0000014c     empty.o (.text.main)
                  00000ca8    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000dc8    00000110     motor.o (.text.Motor_Smooth_Control)
                  00000ed8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000fe4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000010e8    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000011e8    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012d0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000013b4    000000e2     oled.o (.text.OLED_ShowNum)
                  00001496    000000de     oled.o (.text.OLED_Init)
                  00001574    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001650    000000d8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001728    000000d8     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  00001800    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000018d8    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000019a8    000000b8     motor.o (.text.Motor_PID_Control)
                  00001a60    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001b0a    00000002     --HOLE-- [fill = 0]
                  00001b0c    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001bb4    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001c5c    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001cf6    0000009a     oled.o (.text.OLED_ShowString)
                  00001d90    00000090     oled.o (.text.OLED_DrawPoint)
                  00001e20    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001eac    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001f38    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001fc4    00000088     Ganway_Optimized.o (.text.Way_Optimized)
                  0000204c    00000084     Ganway_Optimized.o (.text.Detect_Corner_State)
                  000020d0    00000084     oled.o (.text.OLED_Refresh)
                  00002154    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000021d8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000225a    00000002     --HOLE-- [fill = 0]
                  0000225c    0000007c     Ganway_Optimized.o (.text.Track_Init)
                  000022d8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002354    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  000023c8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000243c    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000024ae    00000002     --HOLE-- [fill = 0]
                  000024b0    0000006c     oled.o (.text.OLED_WR_Byte)
                  0000251c    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002588    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  000025f0    00000068     key.o (.text.Key_1)
                  00002658    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000026c0    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002722    00000002     --HOLE-- [fill = 0]
                  00002724    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002786    00000002     --HOLE-- [fill = 0]
                  00002788    00000060     oled.o (.text.OLED_Clear)
                  000027e8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002846    00000002     --HOLE-- [fill = 0]
                  00002848    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000028a0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000028f4    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002944    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002994    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  000029e0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002a2c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002a76    00000002     --HOLE-- [fill = 0]
                  00002a78    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002ac2    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002b0c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002b54    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002b9c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002be4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002c2c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002c70    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002cb2    00000002     --HOLE-- [fill = 0]
                  00002cb4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002cf4    00000040     key.o (.text.Key)
                  00002d34    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002d74    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002db4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002df0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002e2c    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002e68    0000003c     Ganway_Optimized.o (.text.Handle_Corner_Turn)
                  00002ea4    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002ee0    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002f1c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002f58    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002f94    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002fd0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000300a    00000002     --HOLE-- [fill = 0]
                  0000300c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003046    00000002     --HOLE-- [fill = 0]
                  00003048    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003080    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000030b4    00000034     oled.o (.text.OLED_ColorTurn)
                  000030e8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0000311c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003150    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00003180    00000030     oled.o (.text.OLED_Pow)
                  000031b0    00000030     systick.o (.text.SysTick_Handler)
                  000031e0    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  0000320c    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00003238    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003264    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  00003290    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  000032b8    00000028     empty.o (.text.DL_Common_updateReg)
                  000032e0    00000028     oled.o (.text.DL_Common_updateReg)
                  00003308    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003330    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003358    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003380    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000033a8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000033d0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000033f6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000341c    00000024     motor.o (.text.Left_Control)
                  00003440    00000024     motor.o (.text.Left_Little_Control)
                  00003464    00000024     motor.o (.text.Right_Control)
                  00003488    00000024     motor.o (.text.Right_Little_Control)
                  000034ac    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000034d0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000034f0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003510    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  00003530    00000020     systick.o (.text.delay_ms)
                  00003550    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000356e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000358c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  000035a8    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  000035c4    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000035e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000035fc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003618    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003634    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003650    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000366c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003688    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000036a4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000036c0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000036dc    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000036f8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003710    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003728    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00003740    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003758    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003770    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003788    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000037a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000037b8    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000037d0    00000018     empty.o (.text.DL_GPIO_setPins)
                  000037e8    00000018     motor.o (.text.DL_GPIO_setPins)
                  00003800    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003818    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00003830    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003848    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003860    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003878    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003890    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000038a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000038c0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000038d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000038f0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00003908    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00003920    00000018     empty.o (.text.DL_Timer_startCounter)
                  00003938    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003950    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003968    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003980    00000018     Ganway.o (.text.Way_With_Analog)
                  00003998    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  000039ae    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  000039c4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000039da    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000039f0    00000016     key.o (.text.DL_GPIO_readPins)
                  00003a06    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003a1c    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003a30    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003a44    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003a58    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003a6c    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003a80    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003a94    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003aa8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003abc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003ad0    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003ae4    00000014     key.o (.text.Key_Init_Debounce)
                  00003af8    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003b0a    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003b1c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003b2e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003b40    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003b52    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003b62    00000002     --HOLE-- [fill = 0]
                  00003b64    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003b74    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003b84    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003b94    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003ba4    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003bb2    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003bc0    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003bce    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003bda    00000002     --HOLE-- [fill = 0]
                  00003bdc    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003be8    0000000c     systick.o (.text.get_systicks)
                  00003bf4    0000000c     Scheduler.o (.text.scheduler_init)
                  00003c00    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003c0a    00000002     --HOLE-- [fill = 0]
                  00003c0c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003c14    00000006     libc.a : exit.c.obj (.text:abort)
                  00003c1a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003c1e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003c22    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003c26    00000002     --HOLE-- [fill = 0]
                  00003c28    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003c38    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00003c3c    00000004     --HOLE-- [fill = 0]

.cinit     0    000056c0    00000078     
                  000056c0    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  0000570e    00000002     --HOLE-- [fill = 0]
                  00005710    0000000c     (__TI_handler_table)
                  0000571c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005724    00000010     (__TI_cinit_table)
                  00005734    00000004     --HOLE-- [fill = 0]

.rodata    0    00003c40    00001a80     
                  00003c40    00000d5c     oled.o (.rodata.asc2_2412)
                  0000499c    000005f0     oled.o (.rodata.asc2_1608)
                  00004f8c    00000474     oled.o (.rodata.asc2_1206)
                  00005400    00000228     oled.o (.rodata.asc2_0806)
                  00005628    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005650    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005670    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00005684    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000568e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005690    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005698    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000056a0    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  000056a8    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  000056ae    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  000056b1    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000056b4    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  000056b7    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000056b9    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000025a     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000028     Ganway_Optimized.o (.data.track_ctrl)
                  20200740    00000024     motor.o (.data.motor_pid)
                  20200764    00000010     empty.o (.data.Anolog)
                  20200774    00000010     empty.o (.data.black)
                  20200784    00000010     empty.o (.data.white)
                  20200794    0000000c     key.o (.data.key1_ctrl)
                  202007a0    00000008     systick.o (.data.systicks)
                  202007a8    00000004     empty.o (.data.D_Num)
                  202007ac    00000004     empty.o (.data.Run)
                  202007b0    00000004     systick.o (.data.delay_times)
                  202007b4    00000004     key.o (.data.system_tick_ms)
                  202007b8    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b9    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       Ganway_Optimized.o               1680    32        40     
       motor.o                          1324    14        92     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Ganway.o                         24      0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5390    46        165    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15200   7083      2487   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005724 records: 2, size/record: 8, table size: 16
	.data: load addr=000056c0, load size=0000004e bytes, run addr=20200560, run size=0000025a bytes, compression=lzss
	.bss: load addr=0000571c, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005710 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000033a9     00003c28     00003c22   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003c1b  ADC0_IRQHandler                      
00003c1b  ADC1_IRQHandler                      
00003c1b  AES_IRQHandler                       
00001651  Analyze_Track_State                  
20200764  Anolog                               
00003c1e  C$$EXIT                              
00003c1b  CANFD0_IRQHandler                    
00001b0d  Calculate_Line_Position              
00003291  Calculate_Position_Error             
00003c1b  DAC0_IRQHandler                      
00002cb5  DL_ADC12_setClockConfig              
00003c01  DL_Common_delayCycles                
000027e9  DL_I2C_fillControllerTXFIFO          
000033f7  DL_I2C_setClockConfig                
00001575  DL_SYSCTL_configSYSPLL               
00002c2d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000fe5  DL_Timer_initFourCCPWMMode           
000011e9  DL_Timer_initTimerMode               
000036a5  DL_Timer_setCaptCompUpdateMethod     
00003909  DL_Timer_setCaptureCompareOutCtl     
00003b75  DL_Timer_setCaptureCompareValue      
000036c1  DL_Timer_setClockConfig              
00002b0d  DL_UART_init                         
00003b1d  DL_UART_setClockConfig               
00003c1b  DMA_IRQHandler                       
202007a8  D_Num                                
00003c1b  Default_Handler                      
0000204d  Detect_Corner_State                  
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003c1b  GROUP0_IRQHandler                    
00000ca9  GROUP1_IRQHandler                    
000018d9  Get_Analog_value                     
00002e2d  Get_Anolog_Value                     
00003ba5  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003c1f  HOSTexit                             
00002e69  Handle_Corner_Turn                   
00003969  Handle_Intersection                  
00002589  Handle_Lost_Line                     
00003c1b  HardFault_Handler                    
00003c1b  I2C0_IRQHandler                      
00003c1b  I2C1_IRQHandler                      
00002cf5  Key                                  
000025f1  Key_1                                
00003ae5  Key_Init_Debounce                    
000008ad  Key_Scan_Debounce                    
00003b85  Key_System_Tick_Inc                  
0000341d  Left_Control                         
00003441  Left_Little_Control                  
000019a9  Motor_PID_Control                    
00003511  Motor_Reset_Speed_Monitor            
00000dc9  Motor_Smooth_Control                 
00001bb5  Motor_Speed_Monitor                  
00002355  Motor_Verify_Speed_Consistency       
00003c1b  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
0000243d  No_MCU_Ganv_Sensor_Init_Frist        
00002c71  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002789  OLED_Clear                           
000030b5  OLED_ColorTurn                       
00002b55  OLED_DisplayTurn                     
00001d91  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001497  OLED_Init                            
00003181  OLED_Pow                             
000020d1  OLED_Refresh                         
000000c1  OLED_ShowChar                        
000013b5  OLED_ShowNum                         
00001c5d  OLED_ShowSignedNum                   
00001cf7  OLED_ShowString                      
000024b1  OLED_WR_Byte                         
00003c1b  PendSV_Handler                       
00003c1b  RTC_IRQHandler                       
00003c23  Reset_Handler                        
00003465  Right_Control                        
00003489  Right_Little_Control                 
202007ac  Run                                  
00003c1b  SPI0_IRQHandler                      
00003c1b  SPI1_IRQHandler                      
00003c1b  SVC_Handler                          
00002b9d  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
00002849  SYSCFG_DL_I2C_OLED_init              
00001e21  SYSCFG_DL_PWM_0_init                 
00002be5  SYSCFG_DL_SYSCTL_init                
00003bcf  SYSCFG_DL_SYSTICK_init               
000030e9  SYSCFG_DL_TIMER_0_init               
000028a1  SYSCFG_DL_UART_0_init                
0000311d  SYSCFG_DL_init                       
00001ead  SYSCFG_DL_initPower                  
00000741  Set_PWM                              
000031b1  SysTick_Handler                      
00003c1b  TIMA0_IRQHandler                     
00003c1b  TIMA1_IRQHandler                     
000010e9  TIMG0_IRQHandler                     
00003c1b  TIMG12_IRQHandler                    
00003c1b  TIMG6_IRQHandler                     
00003c1b  TIMG7_IRQHandler                     
00003c1b  TIMG8_IRQHandler                     
00003b2f  TI_memcpy_small                      
00003bc1  TI_memset_small                      
00001729  Track_Adaptive_Control               
00000a09  Track_Basic_Control                  
0000225d  Track_Init                           
00002ea5  Track_PID_Control                    
00002ee1  Track_Weighted_Control               
00002d35  UART0_IRQHandler                     
00003c1b  UART1_IRQHandler                     
00003c1b  UART2_IRQHandler                     
00003c1b  UART3_IRQHandler                     
00001fc5  Way_Optimized                        
00003981  Way_With_Analog                      
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005724  __TI_CINIT_Base                      
00005734  __TI_CINIT_Limit                     
00005734  __TI_CINIT_Warm                      
00005710  __TI_Handler_Table_Base              
0000571c  __TI_Handler_Table_Limit             
00002f95  __TI_auto_init_nobinit_nopinit       
000022d9  __TI_decompress_lzss                 
00003b41  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003b95  __TI_zero_init                       
0000042f  __adddf3                             
0000180b  __addsf3                             
00002a79  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
000026c1  __aeabi_dcmpeq                       
000026fd  __aeabi_dcmpge                       
00002711  __aeabi_dcmpgt                       
000026e9  __aeabi_dcmple                       
000026d5  __aeabi_dcmplt                       
00000ed9  __aeabi_ddiv                         
000012d1  __aeabi_dmul                         
00000425  __aeabi_dsub                         
00003049  __aeabi_f2iz                         
0000180b  __aeabi_fadd                         
00002725  __aeabi_fcmpeq                       
00002761  __aeabi_fcmpge                       
00002775  __aeabi_fcmpgt                       
0000274d  __aeabi_fcmple                       
00002739  __aeabi_fcmplt                       
000021d9  __aeabi_fdiv                         
00001f39  __aeabi_fmul                         
00001801  __aeabi_fsub                         
00003239  __aeabi_i2d                          
00002f1d  __aeabi_i2f                          
000005b7  __aeabi_idiv0                        
00003bdd  __aeabi_memclr                       
00003bdd  __aeabi_memclr4                      
00003bdd  __aeabi_memclr8                      
00003c0d  __aeabi_memcpy                       
00003c0d  __aeabi_memcpy4                      
00003c0d  __aeabi_memcpy8                      
000034ad  __aeabi_ui2d                         
00003381  __aeabi_ui2f                         
00002d75  __aeabi_uidiv                        
00002d75  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002659  __cmpdf2                             
00002fd1  __cmpsf2                             
00000ed9  __divdf3                             
000021d9  __divsf3                             
00002659  __eqdf2                              
00002fd1  __eqsf2                              
00002a79  __fixdfsi                            
00003049  __fixsfsi                            
00003239  __floatsidf                          
00002f1d  __floatsisf                          
000034ad  __floatunsidf                        
00003381  __floatunsisf                        
000023c9  __gedf2                              
00002f59  __gesf2                              
000023c9  __gtdf2                              
00002f59  __gtsf2                              
00002659  __ledf2                              
00002fd1  __lesf2                              
00002659  __ltdf2                              
00002fd1  __ltsf2                              
UNDEFED   __mpu_init                           
000012d1  __muldf3                             
0000300d  __muldsi3                            
00001f39  __mulsf3                             
00002659  __nedf2                              
00002fd1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
00001801  __subsf3                             
000033a9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003c39  _system_pre_init                     
00003c15  abort                                
00002ac3  adc_getValue                         
00005400  asc2_0806                            
00004f8c  asc2_1206                            
0000499c  asc2_1608                            
00003c40  asc2_2412                            
ffffffff  binit                                
20200774  black                                
0000251d  convertAnalogToDigital               
00003531  delay_ms                             
202007b0  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003be9  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
20200794  key1_ctrl                            
00000b5d  main                                 
20200740  motor_pid                            
00001a61  normalizeAnalogValues                
20200560  rx_buff                              
00003bf5  scheduler_init                       
00003bb3  strcpy                               
00003265  strncpy                              
2020055c  task_num                             
20200718  track_ctrl                           
20200660  uart_rx_buffer                       
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20200784  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Set_PWM                              
000008ad  Key_Scan_Debounce                    
00000a09  Track_Basic_Control                  
00000b5d  main                                 
00000ca9  GROUP1_IRQHandler                    
00000dc9  Motor_Smooth_Control                 
00000ed9  __aeabi_ddiv                         
00000ed9  __divdf3                             
00000fe5  DL_Timer_initFourCCPWMMode           
000010e9  TIMG0_IRQHandler                     
000011e9  DL_Timer_initTimerMode               
000012d1  __aeabi_dmul                         
000012d1  __muldf3                             
000013b5  OLED_ShowNum                         
00001497  OLED_Init                            
00001575  DL_SYSCTL_configSYSPLL               
00001651  Analyze_Track_State                  
00001729  Track_Adaptive_Control               
00001801  __aeabi_fsub                         
00001801  __subsf3                             
0000180b  __addsf3                             
0000180b  __aeabi_fadd                         
000018d9  Get_Analog_value                     
000019a9  Motor_PID_Control                    
00001a61  normalizeAnalogValues                
00001b0d  Calculate_Line_Position              
00001bb5  Motor_Speed_Monitor                  
00001c5d  OLED_ShowSignedNum                   
00001cf7  OLED_ShowString                      
00001d91  OLED_DrawPoint                       
00001e21  SYSCFG_DL_PWM_0_init                 
00001ead  SYSCFG_DL_initPower                  
00001f39  __aeabi_fmul                         
00001f39  __mulsf3                             
00001fc5  Way_Optimized                        
0000204d  Detect_Corner_State                  
000020d1  OLED_Refresh                         
000021d9  __aeabi_fdiv                         
000021d9  __divsf3                             
0000225d  Track_Init                           
000022d9  __TI_decompress_lzss                 
00002355  Motor_Verify_Speed_Consistency       
000023c9  __gedf2                              
000023c9  __gtdf2                              
0000243d  No_MCU_Ganv_Sensor_Init_Frist        
000024b1  OLED_WR_Byte                         
0000251d  convertAnalogToDigital               
00002589  Handle_Lost_Line                     
000025f1  Key_1                                
00002659  __cmpdf2                             
00002659  __eqdf2                              
00002659  __ledf2                              
00002659  __ltdf2                              
00002659  __nedf2                              
000026c1  __aeabi_dcmpeq                       
000026d5  __aeabi_dcmplt                       
000026e9  __aeabi_dcmple                       
000026fd  __aeabi_dcmpge                       
00002711  __aeabi_dcmpgt                       
00002725  __aeabi_fcmpeq                       
00002739  __aeabi_fcmplt                       
0000274d  __aeabi_fcmple                       
00002761  __aeabi_fcmpge                       
00002775  __aeabi_fcmpgt                       
00002789  OLED_Clear                           
000027e9  DL_I2C_fillControllerTXFIFO          
00002849  SYSCFG_DL_I2C_OLED_init              
000028a1  SYSCFG_DL_UART_0_init                
00002a79  __aeabi_d2iz                         
00002a79  __fixdfsi                            
00002ac3  adc_getValue                         
00002b0d  DL_UART_init                         
00002b55  OLED_DisplayTurn                     
00002b9d  SYSCFG_DL_ADC12_0_init               
00002be5  SYSCFG_DL_SYSCTL_init                
00002c2d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002c71  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002cb5  DL_ADC12_setClockConfig              
00002cf5  Key                                  
00002d35  UART0_IRQHandler                     
00002d75  __aeabi_uidiv                        
00002d75  __aeabi_uidivmod                     
00002e2d  Get_Anolog_Value                     
00002e69  Handle_Corner_Turn                   
00002ea5  Track_PID_Control                    
00002ee1  Track_Weighted_Control               
00002f1d  __aeabi_i2f                          
00002f1d  __floatsisf                          
00002f59  __gesf2                              
00002f59  __gtsf2                              
00002f95  __TI_auto_init_nobinit_nopinit       
00002fd1  __cmpsf2                             
00002fd1  __eqsf2                              
00002fd1  __lesf2                              
00002fd1  __ltsf2                              
00002fd1  __nesf2                              
0000300d  __muldsi3                            
00003049  __aeabi_f2iz                         
00003049  __fixsfsi                            
000030b5  OLED_ColorTurn                       
000030e9  SYSCFG_DL_TIMER_0_init               
0000311d  SYSCFG_DL_init                       
00003181  OLED_Pow                             
000031b1  SysTick_Handler                      
00003239  __aeabi_i2d                          
00003239  __floatsidf                          
00003265  strncpy                              
00003291  Calculate_Position_Error             
00003381  __aeabi_ui2f                         
00003381  __floatunsisf                        
000033a9  _c_int00_noargs                      
000033f7  DL_I2C_setClockConfig                
0000341d  Left_Control                         
00003441  Left_Little_Control                  
00003465  Right_Control                        
00003489  Right_Little_Control                 
000034ad  __aeabi_ui2d                         
000034ad  __floatunsidf                        
00003511  Motor_Reset_Speed_Monitor            
00003531  delay_ms                             
000036a5  DL_Timer_setCaptCompUpdateMethod     
000036c1  DL_Timer_setClockConfig              
00003909  DL_Timer_setCaptureCompareOutCtl     
00003969  Handle_Intersection                  
00003981  Way_With_Analog                      
00003ae5  Key_Init_Debounce                    
00003b1d  DL_UART_setClockConfig               
00003b2f  TI_memcpy_small                      
00003b41  __TI_decompress_none                 
00003b75  DL_Timer_setCaptureCompareValue      
00003b85  Key_System_Tick_Inc                  
00003b95  __TI_zero_init                       
00003ba5  Get_Digtal_For_User                  
00003bb3  strcpy                               
00003bc1  TI_memset_small                      
00003bcf  SYSCFG_DL_SYSTICK_init               
00003bdd  __aeabi_memclr                       
00003bdd  __aeabi_memclr4                      
00003bdd  __aeabi_memclr8                      
00003be9  get_systicks                         
00003bf5  scheduler_init                       
00003c01  DL_Common_delayCycles                
00003c0d  __aeabi_memcpy                       
00003c0d  __aeabi_memcpy4                      
00003c0d  __aeabi_memcpy8                      
00003c15  abort                                
00003c1b  ADC0_IRQHandler                      
00003c1b  ADC1_IRQHandler                      
00003c1b  AES_IRQHandler                       
00003c1b  CANFD0_IRQHandler                    
00003c1b  DAC0_IRQHandler                      
00003c1b  DMA_IRQHandler                       
00003c1b  Default_Handler                      
00003c1b  GROUP0_IRQHandler                    
00003c1b  HardFault_Handler                    
00003c1b  I2C0_IRQHandler                      
00003c1b  I2C1_IRQHandler                      
00003c1b  NMI_Handler                          
00003c1b  PendSV_Handler                       
00003c1b  RTC_IRQHandler                       
00003c1b  SPI0_IRQHandler                      
00003c1b  SPI1_IRQHandler                      
00003c1b  SVC_Handler                          
00003c1b  TIMA0_IRQHandler                     
00003c1b  TIMA1_IRQHandler                     
00003c1b  TIMG12_IRQHandler                    
00003c1b  TIMG6_IRQHandler                     
00003c1b  TIMG7_IRQHandler                     
00003c1b  TIMG8_IRQHandler                     
00003c1b  UART1_IRQHandler                     
00003c1b  UART2_IRQHandler                     
00003c1b  UART3_IRQHandler                     
00003c1e  C$$EXIT                              
00003c1f  HOSTexit                             
00003c23  Reset_Handler                        
00003c39  _system_pre_init                     
00003c40  asc2_2412                            
0000499c  asc2_1608                            
00004f8c  asc2_1206                            
00005400  asc2_0806                            
00005710  __TI_Handler_Table_Base              
0000571c  __TI_Handler_Table_Limit             
00005724  __TI_CINIT_Base                      
00005734  __TI_CINIT_Limit                     
00005734  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  track_ctrl                           
20200740  motor_pid                            
20200764  Anolog                               
20200774  black                                
20200784  white                                
20200794  key1_ctrl                            
202007a8  D_Num                                
202007ac  Run                                  
202007b0  delay_times                          
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[242 symbols]
