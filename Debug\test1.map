******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 17:06:05 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000033c5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005750  0001a8b0  R  X
  SRAM                  20200000   00008000  000009b7  00007649  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005750   00005750    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003b98   00003b98    r-x .text
  00003c58    00003c58    00001a80   00001a80    r-- .rodata
  000056d8    000056d8    00000078   00000078    r-- .cinit
20200000    20200000    000007ba   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000025a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003b98     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000016c     motor.o (.text.Set_PWM)
                  000008ac    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000a08    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000b5c    0000014c     empty.o (.text.main)
                  00000ca8    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000dc8    00000110     motor.o (.text.Motor_Smooth_Control)
                  00000ed8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000fe4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000010e8    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000011e8    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012d0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000013b4    000000e2     oled.o (.text.OLED_ShowNum)
                  00001496    000000de     oled.o (.text.OLED_Init)
                  00001574    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001650    000000d8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001728    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001800    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000018d0    000000d0     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  000019a0    000000b8     motor.o (.text.Motor_PID_Control)
                  00001a58    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001b02    00000002     --HOLE-- [fill = 0]
                  00001b04    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001bac    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001c54    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001cee    0000009a     oled.o (.text.OLED_ShowString)
                  00001d88    00000090     oled.o (.text.OLED_DrawPoint)
                  00001e18    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001ea4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001f30    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001fbc    00000088     Ganway_Optimized.o (.text.Way_Optimized)
                  00002044    00000084     oled.o (.text.OLED_Refresh)
                  000020c8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000214c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000021ce    00000002     --HOLE-- [fill = 0]
                  000021d0    00000080     Ganway_Optimized.o (.text.Detect_Corner_State)
                  00002250    0000007c     Ganway_Optimized.o (.text.Track_Init)
                  000022cc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002348    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  000023bc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002430    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000024a2    00000002     --HOLE-- [fill = 0]
                  000024a4    0000006c     oled.o (.text.OLED_WR_Byte)
                  00002510    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  0000257c    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  000025e4    00000068     key.o (.text.Key_1)
                  0000264c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000026b4    00000064     Ganway_Optimized.o (.text.Handle_Corner_Turn)
                  00002718    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000277a    00000002     --HOLE-- [fill = 0]
                  0000277c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000027de    00000002     --HOLE-- [fill = 0]
                  000027e0    00000060     oled.o (.text.OLED_Clear)
                  00002840    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000289e    00000002     --HOLE-- [fill = 0]
                  000028a0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000028f8    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000294c    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  0000299c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000029ec    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002a38    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002a84    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002ace    00000002     --HOLE-- [fill = 0]
                  00002ad0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002b1a    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002b64    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002bac    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002bf4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002c3c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002c84    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002cc8    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002d0a    00000002     --HOLE-- [fill = 0]
                  00002d0c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002d4c    00000040     key.o (.text.Key)
                  00002d8c    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002dcc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002e0c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002e48    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002e84    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002ec0    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002efc    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002f38    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002f74    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002fb0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002fec    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003026    00000002     --HOLE-- [fill = 0]
                  00003028    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003062    00000002     --HOLE-- [fill = 0]
                  00003064    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  0000309c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000030d0    00000034     oled.o (.text.OLED_ColorTurn)
                  00003104    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003138    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000316c    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  0000319c    00000030     oled.o (.text.OLED_Pow)
                  000031cc    00000030     systick.o (.text.SysTick_Handler)
                  000031fc    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00003228    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00003254    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003280    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  000032ac    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  000032d4    00000028     empty.o (.text.DL_Common_updateReg)
                  000032fc    00000028     oled.o (.text.DL_Common_updateReg)
                  00003324    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000334c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003374    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000339c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000033c4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000033ec    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003412    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003438    00000024     motor.o (.text.Left_Control)
                  0000345c    00000024     motor.o (.text.Left_Little_Control)
                  00003480    00000024     motor.o (.text.Right_Control)
                  000034a4    00000024     motor.o (.text.Right_Little_Control)
                  000034c8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000034ec    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000350c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  0000352c    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  0000354c    00000020     systick.o (.text.delay_ms)
                  0000356c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000358a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000035a8    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  000035c4    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  000035e0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000035fc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003618    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003634    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003650    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000366c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003688    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000036a4    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000036c0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000036dc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000036f8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003714    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  0000372c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003744    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  0000375c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003774    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000378c    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000037a4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000037bc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000037d4    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000037ec    00000018     empty.o (.text.DL_GPIO_setPins)
                  00003804    00000018     motor.o (.text.DL_GPIO_setPins)
                  0000381c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003834    00000018     empty.o (.text.DL_GPIO_togglePins)
                  0000384c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003864    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000387c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003894    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000038ac    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000038c4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000038dc    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000038f4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000390c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00003924    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000393c    00000018     empty.o (.text.DL_Timer_startCounter)
                  00003954    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000396c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003984    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  0000399c    00000018     Ganway.o (.text.Way_With_Analog)
                  000039b4    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  000039ca    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  000039e0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000039f6    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003a0c    00000016     key.o (.text.DL_GPIO_readPins)
                  00003a22    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003a38    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003a4c    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003a60    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003a74    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003a88    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003a9c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003ab0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003ac4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003ad8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003aec    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003b00    00000014     key.o (.text.Key_Init_Debounce)
                  00003b14    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003b26    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003b38    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003b4a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003b5c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003b6e    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003b7e    00000002     --HOLE-- [fill = 0]
                  00003b80    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003b90    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003ba0    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003bb0    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003bc0    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003bce    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003bdc    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003bea    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003bf6    00000002     --HOLE-- [fill = 0]
                  00003bf8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003c04    0000000c     systick.o (.text.get_systicks)
                  00003c10    0000000c     Scheduler.o (.text.scheduler_init)
                  00003c1c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003c26    00000002     --HOLE-- [fill = 0]
                  00003c28    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003c30    00000006     libc.a : exit.c.obj (.text:abort)
                  00003c36    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003c3a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003c3e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003c42    00000002     --HOLE-- [fill = 0]
                  00003c44    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003c54    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000056d8    00000078     
                  000056d8    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00005726    00000002     --HOLE-- [fill = 0]
                  00005728    0000000c     (__TI_handler_table)
                  00005734    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000573c    00000010     (__TI_cinit_table)
                  0000574c    00000004     --HOLE-- [fill = 0]

.rodata    0    00003c58    00001a80     
                  00003c58    00000d5c     oled.o (.rodata.asc2_2412)
                  000049b4    000005f0     oled.o (.rodata.asc2_1608)
                  00004fa4    00000474     oled.o (.rodata.asc2_1206)
                  00005418    00000228     oled.o (.rodata.asc2_0806)
                  00005640    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005668    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005688    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000569c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000056a6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000056a8    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  000056b0    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000056b8    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  000056c0    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  000056c6    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  000056c9    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000056cc    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  000056cf    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000056d1    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000025a     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000028     Ganway_Optimized.o (.data.track_ctrl)
                  20200740    00000024     motor.o (.data.motor_pid)
                  20200764    00000010     empty.o (.data.Anolog)
                  20200774    00000010     empty.o (.data.black)
                  20200784    00000010     empty.o (.data.white)
                  20200794    0000000c     key.o (.data.key1_ctrl)
                  202007a0    00000008     systick.o (.data.systicks)
                  202007a8    00000004     empty.o (.data.D_Num)
                  202007ac    00000004     empty.o (.data.Run)
                  202007b0    00000004     systick.o (.data.delay_times)
                  202007b4    00000004     key.o (.data.system_tick_ms)
                  202007b8    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b9    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       Ganway_Optimized.o               1708    32        40     
       motor.o                          1324    14        92     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Ganway.o                         24      0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5418    46        165    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15228   7083      2487   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000573c records: 2, size/record: 8, table size: 16
	.data: load addr=000056d8, load size=0000004e bytes, run addr=20200560, run size=0000025a bytes, compression=lzss
	.bss: load addr=00005734, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005728 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000033c5     00003c44     00003c3e   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003c37  ADC0_IRQHandler                      
00003c37  ADC1_IRQHandler                      
00003c37  AES_IRQHandler                       
00001651  Analyze_Track_State                  
20200764  Anolog                               
00003c3a  C$$EXIT                              
00003c37  CANFD0_IRQHandler                    
00001b05  Calculate_Line_Position              
000032ad  Calculate_Position_Error             
00003c37  DAC0_IRQHandler                      
00002d0d  DL_ADC12_setClockConfig              
00003c1d  DL_Common_delayCycles                
00002841  DL_I2C_fillControllerTXFIFO          
00003413  DL_I2C_setClockConfig                
00001575  DL_SYSCTL_configSYSPLL               
00002c85  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000fe5  DL_Timer_initFourCCPWMMode           
000011e9  DL_Timer_initTimerMode               
000036c1  DL_Timer_setCaptCompUpdateMethod     
00003925  DL_Timer_setCaptureCompareOutCtl     
00003b91  DL_Timer_setCaptureCompareValue      
000036dd  DL_Timer_setClockConfig              
00002b65  DL_UART_init                         
00003b39  DL_UART_setClockConfig               
00003c37  DMA_IRQHandler                       
202007a8  D_Num                                
00003c37  Default_Handler                      
000021d1  Detect_Corner_State                  
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003c37  GROUP0_IRQHandler                    
00000ca9  GROUP1_IRQHandler                    
00001801  Get_Analog_value                     
00002e85  Get_Anolog_Value                     
00003bc1  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003c3b  HOSTexit                             
000026b5  Handle_Corner_Turn                   
00003985  Handle_Intersection                  
0000257d  Handle_Lost_Line                     
00003c37  HardFault_Handler                    
00003c37  I2C0_IRQHandler                      
00003c37  I2C1_IRQHandler                      
00002d4d  Key                                  
000025e5  Key_1                                
00003b01  Key_Init_Debounce                    
000008ad  Key_Scan_Debounce                    
00003ba1  Key_System_Tick_Inc                  
00003439  Left_Control                         
0000345d  Left_Little_Control                  
000019a1  Motor_PID_Control                    
0000352d  Motor_Reset_Speed_Monitor            
00000dc9  Motor_Smooth_Control                 
00001bad  Motor_Speed_Monitor                  
00002349  Motor_Verify_Speed_Consistency       
00003c37  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
00002431  No_MCU_Ganv_Sensor_Init_Frist        
00002cc9  No_Mcu_Ganv_Sensor_Task_Without_tick 
000027e1  OLED_Clear                           
000030d1  OLED_ColorTurn                       
00002bad  OLED_DisplayTurn                     
00001d89  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001497  OLED_Init                            
0000319d  OLED_Pow                             
00002045  OLED_Refresh                         
000000c1  OLED_ShowChar                        
000013b5  OLED_ShowNum                         
00001c55  OLED_ShowSignedNum                   
00001cef  OLED_ShowString                      
000024a5  OLED_WR_Byte                         
00003c37  PendSV_Handler                       
00003c37  RTC_IRQHandler                       
00003c3f  Reset_Handler                        
00003481  Right_Control                        
000034a5  Right_Little_Control                 
202007ac  Run                                  
00003c37  SPI0_IRQHandler                      
00003c37  SPI1_IRQHandler                      
00003c37  SVC_Handler                          
00002bf5  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
000028a1  SYSCFG_DL_I2C_OLED_init              
00001e19  SYSCFG_DL_PWM_0_init                 
00002c3d  SYSCFG_DL_SYSCTL_init                
00003beb  SYSCFG_DL_SYSTICK_init               
00003105  SYSCFG_DL_TIMER_0_init               
000028f9  SYSCFG_DL_UART_0_init                
00003139  SYSCFG_DL_init                       
00001ea5  SYSCFG_DL_initPower                  
00000741  Set_PWM                              
000031cd  SysTick_Handler                      
00003c37  TIMA0_IRQHandler                     
00003c37  TIMA1_IRQHandler                     
000010e9  TIMG0_IRQHandler                     
00003c37  TIMG12_IRQHandler                    
00003c37  TIMG6_IRQHandler                     
00003c37  TIMG7_IRQHandler                     
00003c37  TIMG8_IRQHandler                     
00003b4b  TI_memcpy_small                      
00003bdd  TI_memset_small                      
000018d1  Track_Adaptive_Control               
00000a09  Track_Basic_Control                  
00002251  Track_Init                           
00002ec1  Track_PID_Control                    
00002efd  Track_Weighted_Control               
00002d8d  UART0_IRQHandler                     
00003c37  UART1_IRQHandler                     
00003c37  UART2_IRQHandler                     
00003c37  UART3_IRQHandler                     
00001fbd  Way_Optimized                        
0000399d  Way_With_Analog                      
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000573c  __TI_CINIT_Base                      
0000574c  __TI_CINIT_Limit                     
0000574c  __TI_CINIT_Warm                      
00005728  __TI_Handler_Table_Base              
00005734  __TI_Handler_Table_Limit             
00002fb1  __TI_auto_init_nobinit_nopinit       
000022cd  __TI_decompress_lzss                 
00003b5d  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003bb1  __TI_zero_init                       
0000042f  __adddf3                             
00001733  __addsf3                             
00002ad1  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
00002719  __aeabi_dcmpeq                       
00002755  __aeabi_dcmpge                       
00002769  __aeabi_dcmpgt                       
00002741  __aeabi_dcmple                       
0000272d  __aeabi_dcmplt                       
00000ed9  __aeabi_ddiv                         
000012d1  __aeabi_dmul                         
00000425  __aeabi_dsub                         
00003065  __aeabi_f2iz                         
00001733  __aeabi_fadd                         
0000277d  __aeabi_fcmpeq                       
000027b9  __aeabi_fcmpge                       
000027cd  __aeabi_fcmpgt                       
000027a5  __aeabi_fcmple                       
00002791  __aeabi_fcmplt                       
0000214d  __aeabi_fdiv                         
00001f31  __aeabi_fmul                         
00001729  __aeabi_fsub                         
00003255  __aeabi_i2d                          
00002f39  __aeabi_i2f                          
000005b7  __aeabi_idiv0                        
00003bf9  __aeabi_memclr                       
00003bf9  __aeabi_memclr4                      
00003bf9  __aeabi_memclr8                      
00003c29  __aeabi_memcpy                       
00003c29  __aeabi_memcpy4                      
00003c29  __aeabi_memcpy8                      
000034c9  __aeabi_ui2d                         
0000339d  __aeabi_ui2f                         
00002dcd  __aeabi_uidiv                        
00002dcd  __aeabi_uidivmod                     
ffffffff  __binit__                            
0000264d  __cmpdf2                             
00002fed  __cmpsf2                             
00000ed9  __divdf3                             
0000214d  __divsf3                             
0000264d  __eqdf2                              
00002fed  __eqsf2                              
00002ad1  __fixdfsi                            
00003065  __fixsfsi                            
00003255  __floatsidf                          
00002f39  __floatsisf                          
000034c9  __floatunsidf                        
0000339d  __floatunsisf                        
000023bd  __gedf2                              
00002f75  __gesf2                              
000023bd  __gtdf2                              
00002f75  __gtsf2                              
0000264d  __ledf2                              
00002fed  __lesf2                              
0000264d  __ltdf2                              
00002fed  __ltsf2                              
UNDEFED   __mpu_init                           
000012d1  __muldf3                             
00003029  __muldsi3                            
00001f31  __mulsf3                             
0000264d  __nedf2                              
00002fed  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
00001729  __subsf3                             
000033c5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003c55  _system_pre_init                     
00003c31  abort                                
00002b1b  adc_getValue                         
00005418  asc2_0806                            
00004fa4  asc2_1206                            
000049b4  asc2_1608                            
00003c58  asc2_2412                            
ffffffff  binit                                
20200774  black                                
00002511  convertAnalogToDigital               
0000354d  delay_ms                             
202007b0  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003c05  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
20200794  key1_ctrl                            
00000b5d  main                                 
20200740  motor_pid                            
00001a59  normalizeAnalogValues                
20200560  rx_buff                              
00003c11  scheduler_init                       
00003bcf  strcpy                               
00003281  strncpy                              
2020055c  task_num                             
20200718  track_ctrl                           
20200660  uart_rx_buffer                       
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20200784  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Set_PWM                              
000008ad  Key_Scan_Debounce                    
00000a09  Track_Basic_Control                  
00000b5d  main                                 
00000ca9  GROUP1_IRQHandler                    
00000dc9  Motor_Smooth_Control                 
00000ed9  __aeabi_ddiv                         
00000ed9  __divdf3                             
00000fe5  DL_Timer_initFourCCPWMMode           
000010e9  TIMG0_IRQHandler                     
000011e9  DL_Timer_initTimerMode               
000012d1  __aeabi_dmul                         
000012d1  __muldf3                             
000013b5  OLED_ShowNum                         
00001497  OLED_Init                            
00001575  DL_SYSCTL_configSYSPLL               
00001651  Analyze_Track_State                  
00001729  __aeabi_fsub                         
00001729  __subsf3                             
00001733  __addsf3                             
00001733  __aeabi_fadd                         
00001801  Get_Analog_value                     
000018d1  Track_Adaptive_Control               
000019a1  Motor_PID_Control                    
00001a59  normalizeAnalogValues                
00001b05  Calculate_Line_Position              
00001bad  Motor_Speed_Monitor                  
00001c55  OLED_ShowSignedNum                   
00001cef  OLED_ShowString                      
00001d89  OLED_DrawPoint                       
00001e19  SYSCFG_DL_PWM_0_init                 
00001ea5  SYSCFG_DL_initPower                  
00001f31  __aeabi_fmul                         
00001f31  __mulsf3                             
00001fbd  Way_Optimized                        
00002045  OLED_Refresh                         
0000214d  __aeabi_fdiv                         
0000214d  __divsf3                             
000021d1  Detect_Corner_State                  
00002251  Track_Init                           
000022cd  __TI_decompress_lzss                 
00002349  Motor_Verify_Speed_Consistency       
000023bd  __gedf2                              
000023bd  __gtdf2                              
00002431  No_MCU_Ganv_Sensor_Init_Frist        
000024a5  OLED_WR_Byte                         
00002511  convertAnalogToDigital               
0000257d  Handle_Lost_Line                     
000025e5  Key_1                                
0000264d  __cmpdf2                             
0000264d  __eqdf2                              
0000264d  __ledf2                              
0000264d  __ltdf2                              
0000264d  __nedf2                              
000026b5  Handle_Corner_Turn                   
00002719  __aeabi_dcmpeq                       
0000272d  __aeabi_dcmplt                       
00002741  __aeabi_dcmple                       
00002755  __aeabi_dcmpge                       
00002769  __aeabi_dcmpgt                       
0000277d  __aeabi_fcmpeq                       
00002791  __aeabi_fcmplt                       
000027a5  __aeabi_fcmple                       
000027b9  __aeabi_fcmpge                       
000027cd  __aeabi_fcmpgt                       
000027e1  OLED_Clear                           
00002841  DL_I2C_fillControllerTXFIFO          
000028a1  SYSCFG_DL_I2C_OLED_init              
000028f9  SYSCFG_DL_UART_0_init                
00002ad1  __aeabi_d2iz                         
00002ad1  __fixdfsi                            
00002b1b  adc_getValue                         
00002b65  DL_UART_init                         
00002bad  OLED_DisplayTurn                     
00002bf5  SYSCFG_DL_ADC12_0_init               
00002c3d  SYSCFG_DL_SYSCTL_init                
00002c85  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002cc9  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002d0d  DL_ADC12_setClockConfig              
00002d4d  Key                                  
00002d8d  UART0_IRQHandler                     
00002dcd  __aeabi_uidiv                        
00002dcd  __aeabi_uidivmod                     
00002e85  Get_Anolog_Value                     
00002ec1  Track_PID_Control                    
00002efd  Track_Weighted_Control               
00002f39  __aeabi_i2f                          
00002f39  __floatsisf                          
00002f75  __gesf2                              
00002f75  __gtsf2                              
00002fb1  __TI_auto_init_nobinit_nopinit       
00002fed  __cmpsf2                             
00002fed  __eqsf2                              
00002fed  __lesf2                              
00002fed  __ltsf2                              
00002fed  __nesf2                              
00003029  __muldsi3                            
00003065  __aeabi_f2iz                         
00003065  __fixsfsi                            
000030d1  OLED_ColorTurn                       
00003105  SYSCFG_DL_TIMER_0_init               
00003139  SYSCFG_DL_init                       
0000319d  OLED_Pow                             
000031cd  SysTick_Handler                      
00003255  __aeabi_i2d                          
00003255  __floatsidf                          
00003281  strncpy                              
000032ad  Calculate_Position_Error             
0000339d  __aeabi_ui2f                         
0000339d  __floatunsisf                        
000033c5  _c_int00_noargs                      
00003413  DL_I2C_setClockConfig                
00003439  Left_Control                         
0000345d  Left_Little_Control                  
00003481  Right_Control                        
000034a5  Right_Little_Control                 
000034c9  __aeabi_ui2d                         
000034c9  __floatunsidf                        
0000352d  Motor_Reset_Speed_Monitor            
0000354d  delay_ms                             
000036c1  DL_Timer_setCaptCompUpdateMethod     
000036dd  DL_Timer_setClockConfig              
00003925  DL_Timer_setCaptureCompareOutCtl     
00003985  Handle_Intersection                  
0000399d  Way_With_Analog                      
00003b01  Key_Init_Debounce                    
00003b39  DL_UART_setClockConfig               
00003b4b  TI_memcpy_small                      
00003b5d  __TI_decompress_none                 
00003b91  DL_Timer_setCaptureCompareValue      
00003ba1  Key_System_Tick_Inc                  
00003bb1  __TI_zero_init                       
00003bc1  Get_Digtal_For_User                  
00003bcf  strcpy                               
00003bdd  TI_memset_small                      
00003beb  SYSCFG_DL_SYSTICK_init               
00003bf9  __aeabi_memclr                       
00003bf9  __aeabi_memclr4                      
00003bf9  __aeabi_memclr8                      
00003c05  get_systicks                         
00003c11  scheduler_init                       
00003c1d  DL_Common_delayCycles                
00003c29  __aeabi_memcpy                       
00003c29  __aeabi_memcpy4                      
00003c29  __aeabi_memcpy8                      
00003c31  abort                                
00003c37  ADC0_IRQHandler                      
00003c37  ADC1_IRQHandler                      
00003c37  AES_IRQHandler                       
00003c37  CANFD0_IRQHandler                    
00003c37  DAC0_IRQHandler                      
00003c37  DMA_IRQHandler                       
00003c37  Default_Handler                      
00003c37  GROUP0_IRQHandler                    
00003c37  HardFault_Handler                    
00003c37  I2C0_IRQHandler                      
00003c37  I2C1_IRQHandler                      
00003c37  NMI_Handler                          
00003c37  PendSV_Handler                       
00003c37  RTC_IRQHandler                       
00003c37  SPI0_IRQHandler                      
00003c37  SPI1_IRQHandler                      
00003c37  SVC_Handler                          
00003c37  TIMA0_IRQHandler                     
00003c37  TIMA1_IRQHandler                     
00003c37  TIMG12_IRQHandler                    
00003c37  TIMG6_IRQHandler                     
00003c37  TIMG7_IRQHandler                     
00003c37  TIMG8_IRQHandler                     
00003c37  UART1_IRQHandler                     
00003c37  UART2_IRQHandler                     
00003c37  UART3_IRQHandler                     
00003c3a  C$$EXIT                              
00003c3b  HOSTexit                             
00003c3f  Reset_Handler                        
00003c55  _system_pre_init                     
00003c58  asc2_2412                            
000049b4  asc2_1608                            
00004fa4  asc2_1206                            
00005418  asc2_0806                            
00005728  __TI_Handler_Table_Base              
00005734  __TI_Handler_Table_Limit             
0000573c  __TI_CINIT_Base                      
0000574c  __TI_CINIT_Limit                     
0000574c  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  track_ctrl                           
20200740  motor_pid                            
20200764  Anolog                               
20200774  black                                
20200784  white                                
20200794  key1_ctrl                            
202007a8  D_Num                                
202007ac  Run                                  
202007b0  delay_times                          
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[242 symbols]
