******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 16:56:44 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003255


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000055d8  0001aa28  R  X
  SRAM                  20200000   00008000  000009af  00007651  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000055d8   000055d8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003a28   00003a28    r-x .text
  00003ae8    00003ae8    00001a80   00001a80    r-- .rodata
  00005568    00005568    00000070   00000070    r-- .cinit
20200000    20200000    000007b2   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    00000252   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003a28     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000016c     motor.o (.text.Set_PWM)
                  000008ac    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000a08    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000b5c    0000014c     empty.o (.text.main)
                  00000ca8    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000dc8    00000110     motor.o (.text.Motor_Smooth_Control)
                  00000ed8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000fe4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000010e8    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000011e8    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012d0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000013b4    000000e2     oled.o (.text.OLED_ShowNum)
                  00001496    000000de     oled.o (.text.OLED_Init)
                  00001574    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001650    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001728    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000017f8    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  000018b0    000000b8     motor.o (.text.Motor_PID_Control)
                  00001968    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001a12    00000002     --HOLE-- [fill = 0]
                  00001a14    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001abc    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001b64    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001bfe    0000009a     oled.o (.text.OLED_ShowString)
                  00001c98    00000090     oled.o (.text.OLED_DrawPoint)
                  00001d28    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001db4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001e40    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001ecc    00000084     oled.o (.text.OLED_Refresh)
                  00001f50    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  00001fd4    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002058    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000020da    00000002     --HOLE-- [fill = 0]
                  000020dc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002158    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  000021cc    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  00002240    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000022b4    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002326    00000002     --HOLE-- [fill = 0]
                  00002328    00000070     Ganway_Optimized.o (.text.Track_Init)
                  00002398    0000006c     oled.o (.text.OLED_WR_Byte)
                  00002404    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002470    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  000024d8    00000068     key.o (.text.Key_1)
                  00002540    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000025a8    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000260a    00000002     --HOLE-- [fill = 0]
                  0000260c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000266e    00000002     --HOLE-- [fill = 0]
                  00002670    00000060     oled.o (.text.OLED_Clear)
                  000026d0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000272e    00000002     --HOLE-- [fill = 0]
                  00002730    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002788    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000027dc    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  0000282c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000287c    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  000028c8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002914    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000295e    00000002     --HOLE-- [fill = 0]
                  00002960    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000029aa    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  000029f4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002a3c    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002a84    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002acc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002b14    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002b58    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002b9a    00000002     --HOLE-- [fill = 0]
                  00002b9c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002bdc    00000040     key.o (.text.Key)
                  00002c1c    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002c5c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002c9c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002cd8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002d14    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002d50    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002d8c    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002dc8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002e04    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002e40    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002e7c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00002eb6    00000002     --HOLE-- [fill = 0]
                  00002eb8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00002ef2    00000002     --HOLE-- [fill = 0]
                  00002ef4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00002f2c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002f60    00000034     oled.o (.text.OLED_ColorTurn)
                  00002f94    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002fc8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002ffc    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  0000302c    00000030     oled.o (.text.OLED_Pow)
                  0000305c    00000030     systick.o (.text.SysTick_Handler)
                  0000308c    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000030b8    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000030e4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003110    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  0000313c    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  00003164    00000028     empty.o (.text.DL_Common_updateReg)
                  0000318c    00000028     oled.o (.text.DL_Common_updateReg)
                  000031b4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000031dc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003204    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000322c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003254    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000327c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000032a2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000032c8    00000024     motor.o (.text.Left_Control)
                  000032ec    00000024     motor.o (.text.Left_Little_Control)
                  00003310    00000024     motor.o (.text.Right_Control)
                  00003334    00000024     motor.o (.text.Right_Little_Control)
                  00003358    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0000337c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000339c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000033bc    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  000033dc    00000020     systick.o (.text.delay_ms)
                  000033fc    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000341a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003438    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00003454    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00003470    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  0000348c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000034a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000034c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000034e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000034fc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003518    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003534    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003550    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000356c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003588    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000035a4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000035bc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000035d4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000035ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003604    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000361c    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003634    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000364c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003664    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  0000367c    00000018     empty.o (.text.DL_GPIO_setPins)
                  00003694    00000018     motor.o (.text.DL_GPIO_setPins)
                  000036ac    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000036c4    00000018     empty.o (.text.DL_GPIO_togglePins)
                  000036dc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000036f4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000370c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003724    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  0000373c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003754    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000376c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003784    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000379c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000037b4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000037cc    00000018     empty.o (.text.DL_Timer_startCounter)
                  000037e4    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000037fc    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003814    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  0000382c    00000018     Ganway.o (.text.Way_With_Analog)
                  00003844    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  0000385a    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003870    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00003886    00000016     encoder.o (.text.DL_GPIO_readPins)
                  0000389c    00000016     key.o (.text.DL_GPIO_readPins)
                  000038b2    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000038c8    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000038dc    00000014     empty.o (.text.DL_GPIO_clearPins)
                  000038f0    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003904    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003918    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  0000392c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003940    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003954    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003968    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000397c    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003990    00000014     key.o (.text.Key_Init_Debounce)
                  000039a4    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000039b6    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000039c8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000039da    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000039ec    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000039fe    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003a0e    00000002     --HOLE-- [fill = 0]
                  00003a10    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003a20    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003a30    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003a40    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003a50    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003a5e    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003a6c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003a7a    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003a86    00000002     --HOLE-- [fill = 0]
                  00003a88    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003a94    0000000c     systick.o (.text.get_systicks)
                  00003aa0    0000000c     Scheduler.o (.text.scheduler_init)
                  00003aac    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003ab6    00000002     --HOLE-- [fill = 0]
                  00003ab8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003ac0    00000006     libc.a : exit.c.obj (.text:abort)
                  00003ac6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003aca    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003ace    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003ad2    00000002     --HOLE-- [fill = 0]
                  00003ad4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003ae4    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005568    00000070     
                  00005568    0000004a     (.cinit..data.load) [load image, compression = lzss]
                  000055b2    00000002     --HOLE-- [fill = 0]
                  000055b4    0000000c     (__TI_handler_table)
                  000055c0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000055c8    00000010     (__TI_cinit_table)

.rodata    0    00003ae8    00001a80     
                  00003ae8    00000d5c     oled.o (.rodata.asc2_2412)
                  00004844    000005f0     oled.o (.rodata.asc2_1608)
                  00004e34    00000474     oled.o (.rodata.asc2_1206)
                  000052a8    00000228     oled.o (.rodata.asc2_0806)
                  000054d0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000054f8    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005518    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000552c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00005536    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005538    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005540    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00005548    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00005550    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  00005556    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00005559    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000555c    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  0000555f    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005561    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    00000252     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000024     motor.o (.data.motor_pid)
                  2020073c    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  2020075c    00000010     empty.o (.data.Anolog)
                  2020076c    00000010     empty.o (.data.black)
                  2020077c    00000010     empty.o (.data.white)
                  2020078c    0000000c     key.o (.data.key1_ctrl)
                  20200798    00000008     systick.o (.data.systicks)
                  202007a0    00000004     empty.o (.data.D_Num)
                  202007a4    00000004     empty.o (.data.Run)
                  202007a8    00000004     systick.o (.data.delay_times)
                  202007ac    00000004     key.o (.data.system_tick_ms)
                  202007b0    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b1    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       motor.o                          1324    14        92     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway_Optimized.o               1340    32        32     
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Ganway.o                         24      0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5050    46        157    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       110       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     14860   7079      2479   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000055c8 records: 2, size/record: 8, table size: 16
	.data: load addr=00005568, load size=0000004a bytes, run addr=20200560, run size=00000252 bytes, compression=lzss
	.bss: load addr=000055c0, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000055b4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003255     00003ad4     00003ace   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003ac7  ADC0_IRQHandler                      
00003ac7  ADC1_IRQHandler                      
00003ac7  AES_IRQHandler                       
000017f9  Analyze_Track_State                  
2020075c  Anolog                               
00003aca  C$$EXIT                              
00003ac7  CANFD0_IRQHandler                    
00001a15  Calculate_Line_Position              
0000313d  Calculate_Position_Error             
00003ac7  DAC0_IRQHandler                      
00002b9d  DL_ADC12_setClockConfig              
00003aad  DL_Common_delayCycles                
000026d1  DL_I2C_fillControllerTXFIFO          
000032a3  DL_I2C_setClockConfig                
00001575  DL_SYSCTL_configSYSPLL               
00002b15  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000fe5  DL_Timer_initFourCCPWMMode           
000011e9  DL_Timer_initTimerMode               
00003551  DL_Timer_setCaptCompUpdateMethod     
000037b5  DL_Timer_setCaptureCompareOutCtl     
00003a21  DL_Timer_setCaptureCompareValue      
0000356d  DL_Timer_setClockConfig              
000029f5  DL_UART_init                         
000039c9  DL_UART_setClockConfig               
00003ac7  DMA_IRQHandler                       
202007a0  D_Num                                
00003ac7  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003ac7  GROUP0_IRQHandler                    
00000ca9  GROUP1_IRQHandler                    
00001729  Get_Analog_value                     
00002d15  Get_Anolog_Value                     
00003a51  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003acb  HOSTexit                             
00003815  Handle_Intersection                  
00002471  Handle_Lost_Line                     
00003ac7  HardFault_Handler                    
00003ac7  I2C0_IRQHandler                      
00003ac7  I2C1_IRQHandler                      
00002bdd  Key                                  
000024d9  Key_1                                
00003991  Key_Init_Debounce                    
000008ad  Key_Scan_Debounce                    
00003a31  Key_System_Tick_Inc                  
000032c9  Left_Control                         
000032ed  Left_Little_Control                  
000018b1  Motor_PID_Control                    
000033bd  Motor_Reset_Speed_Monitor            
00000dc9  Motor_Smooth_Control                 
00001abd  Motor_Speed_Monitor                  
00002159  Motor_Verify_Speed_Consistency       
00003ac7  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
000022b5  No_MCU_Ganv_Sensor_Init_Frist        
00002b59  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002671  OLED_Clear                           
00002f61  OLED_ColorTurn                       
00002a3d  OLED_DisplayTurn                     
00001c99  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001497  OLED_Init                            
0000302d  OLED_Pow                             
00001ecd  OLED_Refresh                         
000000c1  OLED_ShowChar                        
000013b5  OLED_ShowNum                         
00001b65  OLED_ShowSignedNum                   
00001bff  OLED_ShowString                      
00002399  OLED_WR_Byte                         
00003ac7  PendSV_Handler                       
00003ac7  RTC_IRQHandler                       
00003acf  Reset_Handler                        
00003311  Right_Control                        
00003335  Right_Little_Control                 
202007a4  Run                                  
00003ac7  SPI0_IRQHandler                      
00003ac7  SPI1_IRQHandler                      
00003ac7  SVC_Handler                          
00002a85  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
00002731  SYSCFG_DL_I2C_OLED_init              
00001d29  SYSCFG_DL_PWM_0_init                 
00002acd  SYSCFG_DL_SYSCTL_init                
00003a7b  SYSCFG_DL_SYSTICK_init               
00002f95  SYSCFG_DL_TIMER_0_init               
00002789  SYSCFG_DL_UART_0_init                
00002fc9  SYSCFG_DL_init                       
00001db5  SYSCFG_DL_initPower                  
00000741  Set_PWM                              
0000305d  SysTick_Handler                      
00003ac7  TIMA0_IRQHandler                     
00003ac7  TIMA1_IRQHandler                     
000010e9  TIMG0_IRQHandler                     
00003ac7  TIMG12_IRQHandler                    
00003ac7  TIMG6_IRQHandler                     
00003ac7  TIMG7_IRQHandler                     
00003ac7  TIMG8_IRQHandler                     
000039db  TI_memcpy_small                      
00003a6d  TI_memset_small                      
000021cd  Track_Adaptive_Control               
00000a09  Track_Basic_Control                  
00002329  Track_Init                           
00002d51  Track_PID_Control                    
00002d8d  Track_Weighted_Control               
00002c1d  UART0_IRQHandler                     
00003ac7  UART1_IRQHandler                     
00003ac7  UART2_IRQHandler                     
00003ac7  UART3_IRQHandler                     
00001f51  Way_Optimized                        
0000382d  Way_With_Analog                      
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000055c8  __TI_CINIT_Base                      
000055d8  __TI_CINIT_Limit                     
000055d8  __TI_CINIT_Warm                      
000055b4  __TI_Handler_Table_Base              
000055c0  __TI_Handler_Table_Limit             
00002e41  __TI_auto_init_nobinit_nopinit       
000020dd  __TI_decompress_lzss                 
000039ed  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003a41  __TI_zero_init                       
0000042f  __adddf3                             
0000165b  __addsf3                             
00002961  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
000025a9  __aeabi_dcmpeq                       
000025e5  __aeabi_dcmpge                       
000025f9  __aeabi_dcmpgt                       
000025d1  __aeabi_dcmple                       
000025bd  __aeabi_dcmplt                       
00000ed9  __aeabi_ddiv                         
000012d1  __aeabi_dmul                         
00000425  __aeabi_dsub                         
00002ef5  __aeabi_f2iz                         
0000165b  __aeabi_fadd                         
0000260d  __aeabi_fcmpeq                       
00002649  __aeabi_fcmpge                       
0000265d  __aeabi_fcmpgt                       
00002635  __aeabi_fcmple                       
00002621  __aeabi_fcmplt                       
00002059  __aeabi_fdiv                         
00001e41  __aeabi_fmul                         
00001651  __aeabi_fsub                         
000030e5  __aeabi_i2d                          
00002dc9  __aeabi_i2f                          
000005b7  __aeabi_idiv0                        
00003a89  __aeabi_memclr                       
00003a89  __aeabi_memclr4                      
00003a89  __aeabi_memclr8                      
00003ab9  __aeabi_memcpy                       
00003ab9  __aeabi_memcpy4                      
00003ab9  __aeabi_memcpy8                      
00003359  __aeabi_ui2d                         
0000322d  __aeabi_ui2f                         
00002c5d  __aeabi_uidiv                        
00002c5d  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002541  __cmpdf2                             
00002e7d  __cmpsf2                             
00000ed9  __divdf3                             
00002059  __divsf3                             
00002541  __eqdf2                              
00002e7d  __eqsf2                              
00002961  __fixdfsi                            
00002ef5  __fixsfsi                            
000030e5  __floatsidf                          
00002dc9  __floatsisf                          
00003359  __floatunsidf                        
0000322d  __floatunsisf                        
00002241  __gedf2                              
00002e05  __gesf2                              
00002241  __gtdf2                              
00002e05  __gtsf2                              
00002541  __ledf2                              
00002e7d  __lesf2                              
00002541  __ltdf2                              
00002e7d  __ltsf2                              
UNDEFED   __mpu_init                           
000012d1  __muldf3                             
00002eb9  __muldsi3                            
00001e41  __mulsf3                             
00002541  __nedf2                              
00002e7d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
00001651  __subsf3                             
00003255  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003ae5  _system_pre_init                     
00003ac1  abort                                
000029ab  adc_getValue                         
000052a8  asc2_0806                            
00004e34  asc2_1206                            
00004844  asc2_1608                            
00003ae8  asc2_2412                            
ffffffff  binit                                
2020076c  black                                
00002405  convertAnalogToDigital               
000033dd  delay_ms                             
202007a8  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003a95  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
2020078c  key1_ctrl                            
00000b5d  main                                 
20200718  motor_pid                            
00001969  normalizeAnalogValues                
20200560  rx_buff                              
00003aa1  scheduler_init                       
00003a5f  strcpy                               
00003111  strncpy                              
2020055c  task_num                             
2020073c  track_ctrl                           
20200660  uart_rx_buffer                       
202007b0  uart_rx_index                        
202007b1  uart_rx_ticks                        
2020077c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Set_PWM                              
000008ad  Key_Scan_Debounce                    
00000a09  Track_Basic_Control                  
00000b5d  main                                 
00000ca9  GROUP1_IRQHandler                    
00000dc9  Motor_Smooth_Control                 
00000ed9  __aeabi_ddiv                         
00000ed9  __divdf3                             
00000fe5  DL_Timer_initFourCCPWMMode           
000010e9  TIMG0_IRQHandler                     
000011e9  DL_Timer_initTimerMode               
000012d1  __aeabi_dmul                         
000012d1  __muldf3                             
000013b5  OLED_ShowNum                         
00001497  OLED_Init                            
00001575  DL_SYSCTL_configSYSPLL               
00001651  __aeabi_fsub                         
00001651  __subsf3                             
0000165b  __addsf3                             
0000165b  __aeabi_fadd                         
00001729  Get_Analog_value                     
000017f9  Analyze_Track_State                  
000018b1  Motor_PID_Control                    
00001969  normalizeAnalogValues                
00001a15  Calculate_Line_Position              
00001abd  Motor_Speed_Monitor                  
00001b65  OLED_ShowSignedNum                   
00001bff  OLED_ShowString                      
00001c99  OLED_DrawPoint                       
00001d29  SYSCFG_DL_PWM_0_init                 
00001db5  SYSCFG_DL_initPower                  
00001e41  __aeabi_fmul                         
00001e41  __mulsf3                             
00001ecd  OLED_Refresh                         
00001f51  Way_Optimized                        
00002059  __aeabi_fdiv                         
00002059  __divsf3                             
000020dd  __TI_decompress_lzss                 
00002159  Motor_Verify_Speed_Consistency       
000021cd  Track_Adaptive_Control               
00002241  __gedf2                              
00002241  __gtdf2                              
000022b5  No_MCU_Ganv_Sensor_Init_Frist        
00002329  Track_Init                           
00002399  OLED_WR_Byte                         
00002405  convertAnalogToDigital               
00002471  Handle_Lost_Line                     
000024d9  Key_1                                
00002541  __cmpdf2                             
00002541  __eqdf2                              
00002541  __ledf2                              
00002541  __ltdf2                              
00002541  __nedf2                              
000025a9  __aeabi_dcmpeq                       
000025bd  __aeabi_dcmplt                       
000025d1  __aeabi_dcmple                       
000025e5  __aeabi_dcmpge                       
000025f9  __aeabi_dcmpgt                       
0000260d  __aeabi_fcmpeq                       
00002621  __aeabi_fcmplt                       
00002635  __aeabi_fcmple                       
00002649  __aeabi_fcmpge                       
0000265d  __aeabi_fcmpgt                       
00002671  OLED_Clear                           
000026d1  DL_I2C_fillControllerTXFIFO          
00002731  SYSCFG_DL_I2C_OLED_init              
00002789  SYSCFG_DL_UART_0_init                
00002961  __aeabi_d2iz                         
00002961  __fixdfsi                            
000029ab  adc_getValue                         
000029f5  DL_UART_init                         
00002a3d  OLED_DisplayTurn                     
00002a85  SYSCFG_DL_ADC12_0_init               
00002acd  SYSCFG_DL_SYSCTL_init                
00002b15  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002b59  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002b9d  DL_ADC12_setClockConfig              
00002bdd  Key                                  
00002c1d  UART0_IRQHandler                     
00002c5d  __aeabi_uidiv                        
00002c5d  __aeabi_uidivmod                     
00002d15  Get_Anolog_Value                     
00002d51  Track_PID_Control                    
00002d8d  Track_Weighted_Control               
00002dc9  __aeabi_i2f                          
00002dc9  __floatsisf                          
00002e05  __gesf2                              
00002e05  __gtsf2                              
00002e41  __TI_auto_init_nobinit_nopinit       
00002e7d  __cmpsf2                             
00002e7d  __eqsf2                              
00002e7d  __lesf2                              
00002e7d  __ltsf2                              
00002e7d  __nesf2                              
00002eb9  __muldsi3                            
00002ef5  __aeabi_f2iz                         
00002ef5  __fixsfsi                            
00002f61  OLED_ColorTurn                       
00002f95  SYSCFG_DL_TIMER_0_init               
00002fc9  SYSCFG_DL_init                       
0000302d  OLED_Pow                             
0000305d  SysTick_Handler                      
000030e5  __aeabi_i2d                          
000030e5  __floatsidf                          
00003111  strncpy                              
0000313d  Calculate_Position_Error             
0000322d  __aeabi_ui2f                         
0000322d  __floatunsisf                        
00003255  _c_int00_noargs                      
000032a3  DL_I2C_setClockConfig                
000032c9  Left_Control                         
000032ed  Left_Little_Control                  
00003311  Right_Control                        
00003335  Right_Little_Control                 
00003359  __aeabi_ui2d                         
00003359  __floatunsidf                        
000033bd  Motor_Reset_Speed_Monitor            
000033dd  delay_ms                             
00003551  DL_Timer_setCaptCompUpdateMethod     
0000356d  DL_Timer_setClockConfig              
000037b5  DL_Timer_setCaptureCompareOutCtl     
00003815  Handle_Intersection                  
0000382d  Way_With_Analog                      
00003991  Key_Init_Debounce                    
000039c9  DL_UART_setClockConfig               
000039db  TI_memcpy_small                      
000039ed  __TI_decompress_none                 
00003a21  DL_Timer_setCaptureCompareValue      
00003a31  Key_System_Tick_Inc                  
00003a41  __TI_zero_init                       
00003a51  Get_Digtal_For_User                  
00003a5f  strcpy                               
00003a6d  TI_memset_small                      
00003a7b  SYSCFG_DL_SYSTICK_init               
00003a89  __aeabi_memclr                       
00003a89  __aeabi_memclr4                      
00003a89  __aeabi_memclr8                      
00003a95  get_systicks                         
00003aa1  scheduler_init                       
00003aad  DL_Common_delayCycles                
00003ab9  __aeabi_memcpy                       
00003ab9  __aeabi_memcpy4                      
00003ab9  __aeabi_memcpy8                      
00003ac1  abort                                
00003ac7  ADC0_IRQHandler                      
00003ac7  ADC1_IRQHandler                      
00003ac7  AES_IRQHandler                       
00003ac7  CANFD0_IRQHandler                    
00003ac7  DAC0_IRQHandler                      
00003ac7  DMA_IRQHandler                       
00003ac7  Default_Handler                      
00003ac7  GROUP0_IRQHandler                    
00003ac7  HardFault_Handler                    
00003ac7  I2C0_IRQHandler                      
00003ac7  I2C1_IRQHandler                      
00003ac7  NMI_Handler                          
00003ac7  PendSV_Handler                       
00003ac7  RTC_IRQHandler                       
00003ac7  SPI0_IRQHandler                      
00003ac7  SPI1_IRQHandler                      
00003ac7  SVC_Handler                          
00003ac7  TIMA0_IRQHandler                     
00003ac7  TIMA1_IRQHandler                     
00003ac7  TIMG12_IRQHandler                    
00003ac7  TIMG6_IRQHandler                     
00003ac7  TIMG7_IRQHandler                     
00003ac7  TIMG8_IRQHandler                     
00003ac7  UART1_IRQHandler                     
00003ac7  UART2_IRQHandler                     
00003ac7  UART3_IRQHandler                     
00003aca  C$$EXIT                              
00003acb  HOSTexit                             
00003acf  Reset_Handler                        
00003ae5  _system_pre_init                     
00003ae8  asc2_2412                            
00004844  asc2_1608                            
00004e34  asc2_1206                            
000052a8  asc2_0806                            
000055b4  __TI_Handler_Table_Base              
000055c0  __TI_Handler_Table_Limit             
000055c8  __TI_CINIT_Base                      
000055d8  __TI_CINIT_Limit                     
000055d8  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  motor_pid                            
2020073c  track_ctrl                           
2020075c  Anolog                               
2020076c  black                                
2020077c  white                                
2020078c  key1_ctrl                            
202007a0  D_Num                                
202007a4  Run                                  
202007a8  delay_times                          
202007b0  uart_rx_index                        
202007b1  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[240 symbols]
