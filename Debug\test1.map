******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 16:28:53 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000304d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005360  0001aca0  R  X
  SRAM                  20200000   00008000  00000977  00007689  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005360   00005360    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000037b0   000037b0    r-x .text
  00003870    00003870    00001a70   00001a70    r-- .rodata
  000052e0    000052e0    00000080   00000080    r-- .cinit
20200000    20200000    0000077a   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000021a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000037b0     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000015c     key.o (.text.Key_Scan_Debounce)
                  0000089c    0000015c     motor.o (.text.Set_PWM)
                  000009f8    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000b4c    00000138     empty.o (.text.main)
                  00000c84    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000da4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000eb0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000fb4    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000010b4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000119c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001280    000000e2     oled.o (.text.OLED_ShowNum)
                  00001362    000000de     oled.o (.text.OLED_Init)
                  00001440    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000151c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000015f4    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000016c4    000000bc     motor.o (.text.Motor_Smooth_Control)
                  00001780    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001838    000000b8     motor.o (.text.Motor_PID_Control)
                  000018f0    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000199a    00000002     --HOLE-- [fill = 0]
                  0000199c    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001a44    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001ade    0000009a     oled.o (.text.OLED_ShowString)
                  00001b78    00000090     oled.o (.text.OLED_DrawPoint)
                  00001c08    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001c94    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001d20    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001dac    00000084     oled.o (.text.OLED_Refresh)
                  00001e30    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  00001eb4    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001f38    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00001fba    00000002     --HOLE-- [fill = 0]
                  00001fbc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002038    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  000020ac    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002120    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002192    00000002     --HOLE-- [fill = 0]
                  00002194    0000006c     oled.o (.text.OLED_WR_Byte)
                  00002200    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  0000226c    00000068     key.o (.text.Key_1)
                  000022d4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000233c    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000239e    00000002     --HOLE-- [fill = 0]
                  000023a0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002402    00000002     --HOLE-- [fill = 0]
                  00002404    00000060     oled.o (.text.OLED_Clear)
                  00002464    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000024c2    00000002     --HOLE-- [fill = 0]
                  000024c4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000251c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002570    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000025c0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002610    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  0000265c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000026a8    0000004c     Ganway_Optimized.o (.text.Track_Init)
                  000026f4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000273e    00000002     --HOLE-- [fill = 0]
                  00002740    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000278a    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  000027d4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000281c    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002864    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  000028ac    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000028f4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002938    00000044     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  0000297c    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000029be    00000002     --HOLE-- [fill = 0]
                  000029c0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002a00    00000040     key.o (.text.Key)
                  00002a40    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002a80    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002ac0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002afc    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002b38    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002b74    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002bb0    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002bec    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002c28    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002c64    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002ca0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00002cda    00000002     --HOLE-- [fill = 0]
                  00002cdc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00002d16    00000002     --HOLE-- [fill = 0]
                  00002d18    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00002d50    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002d84    00000034     oled.o (.text.OLED_ColorTurn)
                  00002db8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002dec    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002e20    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00002e50    00000030     oled.o (.text.OLED_Pow)
                  00002e80    00000030     systick.o (.text.SysTick_Handler)
                  00002eb0    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00002edc    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002f08    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002f34    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  00002f5c    00000028     empty.o (.text.DL_Common_updateReg)
                  00002f84    00000028     oled.o (.text.DL_Common_updateReg)
                  00002fac    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002fd4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00002ffc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003024    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000304c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003074    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000309a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000030c0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000030e4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003104    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003124    00000020     systick.o (.text.delay_ms)
                  00003144    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003162    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003180    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  0000319c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  000031b8    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000031d4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000031f0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000320c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003228    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003244    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003260    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000327c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003298    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000032b4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000032d0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000032ec    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003304    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0000331c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00003334    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000334c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003364    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000337c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003394    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000033ac    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000033c4    00000018     empty.o (.text.DL_GPIO_setPins)
                  000033dc    00000018     motor.o (.text.DL_GPIO_setPins)
                  000033f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000340c    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00003424    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000343c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003454    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000346c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003484    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000349c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000034b4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000034cc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000034e4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000034fc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00003514    00000018     empty.o (.text.DL_Timer_startCounter)
                  0000352c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003544    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000355c    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003574    00000018     Ganway.o (.text.Way_With_Analog)
                  0000358c    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  000035a2    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  000035b8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000035ce    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000035e4    00000016     key.o (.text.DL_GPIO_readPins)
                  000035fa    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003610    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003624    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003638    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000364c    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003660    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003674    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003688    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000369c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000036b0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000036c4    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  000036d8    00000014     key.o (.text.Key_Init_Debounce)
                  000036ec    00000014     motor.o (.text.Left_Control)
                  00003700    00000014     motor.o (.text.Left_Little_Control)
                  00003714    00000014     motor.o (.text.Right_Control)
                  00003728    00000014     motor.o (.text.Right_Little_Control)
                  0000373c    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  0000374e    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003760    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003772    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003784    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003796    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  000037a6    00000002     --HOLE-- [fill = 0]
                  000037a8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000037b8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000037c8    00000010     key.o (.text.Key_System_Tick_Inc)
                  000037d8    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  000037e8    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  000037f6    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003804    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003810    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000381c    0000000c     systick.o (.text.get_systicks)
                  00003828    0000000c     Scheduler.o (.text.scheduler_init)
                  00003834    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000383e    00000002     --HOLE-- [fill = 0]
                  00003840    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003848    00000006     libc.a : exit.c.obj (.text:abort)
                  0000384e    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003852    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003856    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000385a    00000002     --HOLE-- [fill = 0]
                  0000385c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000386c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000052e0    00000080     
                  000052e0    00000059     (.cinit..data.load) [load image, compression = lzss]
                  00005339    00000003     --HOLE-- [fill = 0]
                  0000533c    0000000c     (__TI_handler_table)
                  00005348    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005350    00000010     (__TI_cinit_table)

.rodata    0    00003870    00001a70     
                  00003870    00000d5c     oled.o (.rodata.asc2_2412)
                  000045cc    000005f0     oled.o (.rodata.asc2_1608)
                  00004bbc    00000474     oled.o (.rodata.asc2_1206)
                  00005030    00000228     oled.o (.rodata.asc2_0806)
                  00005258    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005280    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  000052a0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000052b4    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000052be    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000052c0    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  000052c8    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000052d0    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  000052d3    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000052d6    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  000052d9    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000052db    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000021a     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000024     motor.o (.data.motor_pid)
                  20200704    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  20200724    00000010     empty.o (.data.Anolog)
                  20200734    00000010     empty.o (.data.black)
                  20200744    00000010     empty.o (.data.white)
                  20200754    0000000c     key.o (.data.key1_ctrl)
                  20200760    00000008     systick.o (.data.systicks)
                  20200768    00000004     empty.o (.data.D_Num)
                  2020076c    00000004     empty.o (.data.Run)
                  20200770    00000004     systick.o (.data.delay_times)
                  20200774    00000004     key.o (.data.system_tick_ms)
                  20200778    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200779    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          882     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3662    291       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway_Optimized.o               1268    32        32     
       motor.o                          844     0         36     
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Ganway.o                         24      0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4498    32        101    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           316     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       125       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     14230   7080      2423   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005350 records: 2, size/record: 8, table size: 16
	.data: load addr=000052e0, load size=00000059 bytes, run addr=20200560, run size=0000021a bytes, compression=lzss
	.bss: load addr=00005348, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000533c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   0000304d     0000385c     00003856   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000384f  ADC0_IRQHandler                      
0000384f  ADC1_IRQHandler                      
0000384f  AES_IRQHandler                       
00001781  Analyze_Track_State                  
20200724  Anolog                               
00003852  C$$EXIT                              
0000384f  CANFD0_IRQHandler                    
0000199d  Calculate_Line_Position              
00002f35  Calculate_Position_Error             
0000384f  DAC0_IRQHandler                      
000029c1  DL_ADC12_setClockConfig              
00003835  DL_Common_delayCycles                
00002465  DL_I2C_fillControllerTXFIFO          
0000309b  DL_I2C_setClockConfig                
00001441  DL_SYSCTL_configSYSPLL               
000028f5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000eb1  DL_Timer_initFourCCPWMMode           
000010b5  DL_Timer_initTimerMode               
00003299  DL_Timer_setCaptCompUpdateMethod     
000034fd  DL_Timer_setCaptureCompareOutCtl     
000037b9  DL_Timer_setCaptureCompareValue      
000032b5  DL_Timer_setClockConfig              
000027d5  DL_UART_init                         
00003761  DL_UART_setClockConfig               
0000384f  DMA_IRQHandler                       
20200768  D_Num                                
0000384f  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
0000384f  GROUP0_IRQHandler                    
00000c85  GROUP1_IRQHandler                    
000015f5  Get_Analog_value                     
00002b39  Get_Anolog_Value                     
000037e9  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003853  HOSTexit                             
0000355d  Handle_Intersection                  
00002939  Handle_Lost_Line                     
0000384f  HardFault_Handler                    
0000384f  I2C0_IRQHandler                      
0000384f  I2C1_IRQHandler                      
00002a01  Key                                  
0000226d  Key_1                                
000036d9  Key_Init_Debounce                    
00000741  Key_Scan_Debounce                    
000037c9  Key_System_Tick_Inc                  
000036ed  Left_Control                         
00003701  Left_Little_Control                  
00001839  Motor_PID_Control                    
000016c5  Motor_Smooth_Control                 
0000384f  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
00002121  No_MCU_Ganv_Sensor_Init_Frist        
0000297d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002405  OLED_Clear                           
00002d85  OLED_ColorTurn                       
0000281d  OLED_DisplayTurn                     
00001b79  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001363  OLED_Init                            
00002e51  OLED_Pow                             
00001dad  OLED_Refresh                         
000000c1  OLED_ShowChar                        
00001281  OLED_ShowNum                         
00001a45  OLED_ShowSignedNum                   
00001adf  OLED_ShowString                      
00002195  OLED_WR_Byte                         
0000384f  PendSV_Handler                       
0000384f  RTC_IRQHandler                       
00003857  Reset_Handler                        
00003715  Right_Control                        
00003729  Right_Little_Control                 
2020076c  Run                                  
0000384f  SPI0_IRQHandler                      
0000384f  SPI1_IRQHandler                      
0000384f  SVC_Handler                          
00002865  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
000024c5  SYSCFG_DL_I2C_OLED_init              
00001c09  SYSCFG_DL_PWM_0_init                 
000028ad  SYSCFG_DL_SYSCTL_init                
00003805  SYSCFG_DL_SYSTICK_init               
00002db9  SYSCFG_DL_TIMER_0_init               
0000251d  SYSCFG_DL_UART_0_init                
00002ded  SYSCFG_DL_init                       
00001c95  SYSCFG_DL_initPower                  
0000089d  Set_PWM                              
00002e81  SysTick_Handler                      
0000384f  TIMA0_IRQHandler                     
0000384f  TIMA1_IRQHandler                     
00000fb5  TIMG0_IRQHandler                     
0000384f  TIMG12_IRQHandler                    
0000384f  TIMG6_IRQHandler                     
0000384f  TIMG7_IRQHandler                     
0000384f  TIMG8_IRQHandler                     
00003773  TI_memcpy_small                      
000037f7  TI_memset_small                      
00002039  Track_Adaptive_Control               
000009f9  Track_Basic_Control                  
000026a9  Track_Init                           
00002b75  Track_PID_Control                    
00002bb1  Track_Weighted_Control               
00002a41  UART0_IRQHandler                     
0000384f  UART1_IRQHandler                     
0000384f  UART2_IRQHandler                     
0000384f  UART3_IRQHandler                     
00001e31  Way_Optimized                        
00003575  Way_With_Analog                      
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005350  __TI_CINIT_Base                      
00005360  __TI_CINIT_Limit                     
00005360  __TI_CINIT_Warm                      
0000533c  __TI_Handler_Table_Base              
00005348  __TI_Handler_Table_Limit             
00002c65  __TI_auto_init_nobinit_nopinit       
00001fbd  __TI_decompress_lzss                 
00003785  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000037d9  __TI_zero_init                       
0000042f  __adddf3                             
00001527  __addsf3                             
00002741  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
0000233d  __aeabi_dcmpeq                       
00002379  __aeabi_dcmpge                       
0000238d  __aeabi_dcmpgt                       
00002365  __aeabi_dcmple                       
00002351  __aeabi_dcmplt                       
00000da5  __aeabi_ddiv                         
0000119d  __aeabi_dmul                         
00000425  __aeabi_dsub                         
00002d19  __aeabi_f2iz                         
00001527  __aeabi_fadd                         
000023a1  __aeabi_fcmpeq                       
000023dd  __aeabi_fcmpge                       
000023f1  __aeabi_fcmpgt                       
000023c9  __aeabi_fcmple                       
000023b5  __aeabi_fcmplt                       
00001f39  __aeabi_fdiv                         
00001d21  __aeabi_fmul                         
0000151d  __aeabi_fsub                         
00002f09  __aeabi_i2d                          
00002bed  __aeabi_i2f                          
000005b7  __aeabi_idiv0                        
00003811  __aeabi_memclr                       
00003811  __aeabi_memclr4                      
00003811  __aeabi_memclr8                      
00003841  __aeabi_memcpy                       
00003841  __aeabi_memcpy4                      
00003841  __aeabi_memcpy8                      
000030c1  __aeabi_ui2d                         
00003025  __aeabi_ui2f                         
00002a81  __aeabi_uidiv                        
00002a81  __aeabi_uidivmod                     
ffffffff  __binit__                            
000022d5  __cmpdf2                             
00002ca1  __cmpsf2                             
00000da5  __divdf3                             
00001f39  __divsf3                             
000022d5  __eqdf2                              
00002ca1  __eqsf2                              
00002741  __fixdfsi                            
00002d19  __fixsfsi                            
00002f09  __floatsidf                          
00002bed  __floatsisf                          
000030c1  __floatunsidf                        
00003025  __floatunsisf                        
000020ad  __gedf2                              
00002c29  __gesf2                              
000020ad  __gtdf2                              
00002c29  __gtsf2                              
000022d5  __ledf2                              
00002ca1  __lesf2                              
000022d5  __ltdf2                              
00002ca1  __ltsf2                              
UNDEFED   __mpu_init                           
0000119d  __muldf3                             
00002cdd  __muldsi3                            
00001d21  __mulsf3                             
000022d5  __nedf2                              
00002ca1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
0000151d  __subsf3                             
0000304d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000386d  _system_pre_init                     
00003849  abort                                
0000278b  adc_getValue                         
00005030  asc2_0806                            
00004bbc  asc2_1206                            
000045cc  asc2_1608                            
00003870  asc2_2412                            
ffffffff  binit                                
20200734  black                                
00002201  convertAnalogToDigital               
00003125  delay_ms                             
20200770  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
0000381d  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
20200754  key1_ctrl                            
00000b4d  main                                 
202006e0  motor_pid                            
000018f1  normalizeAnalogValues                
20200560  rx_buff                              
00003829  scheduler_init                       
2020055c  task_num                             
20200704  track_ctrl                           
20200660  uart_rx_buffer                       
20200778  uart_rx_index                        
20200779  uart_rx_ticks                        
20200744  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Key_Scan_Debounce                    
0000089d  Set_PWM                              
000009f9  Track_Basic_Control                  
00000b4d  main                                 
00000c85  GROUP1_IRQHandler                    
00000da5  __aeabi_ddiv                         
00000da5  __divdf3                             
00000eb1  DL_Timer_initFourCCPWMMode           
00000fb5  TIMG0_IRQHandler                     
000010b5  DL_Timer_initTimerMode               
0000119d  __aeabi_dmul                         
0000119d  __muldf3                             
00001281  OLED_ShowNum                         
00001363  OLED_Init                            
00001441  DL_SYSCTL_configSYSPLL               
0000151d  __aeabi_fsub                         
0000151d  __subsf3                             
00001527  __addsf3                             
00001527  __aeabi_fadd                         
000015f5  Get_Analog_value                     
000016c5  Motor_Smooth_Control                 
00001781  Analyze_Track_State                  
00001839  Motor_PID_Control                    
000018f1  normalizeAnalogValues                
0000199d  Calculate_Line_Position              
00001a45  OLED_ShowSignedNum                   
00001adf  OLED_ShowString                      
00001b79  OLED_DrawPoint                       
00001c09  SYSCFG_DL_PWM_0_init                 
00001c95  SYSCFG_DL_initPower                  
00001d21  __aeabi_fmul                         
00001d21  __mulsf3                             
00001dad  OLED_Refresh                         
00001e31  Way_Optimized                        
00001f39  __aeabi_fdiv                         
00001f39  __divsf3                             
00001fbd  __TI_decompress_lzss                 
00002039  Track_Adaptive_Control               
000020ad  __gedf2                              
000020ad  __gtdf2                              
00002121  No_MCU_Ganv_Sensor_Init_Frist        
00002195  OLED_WR_Byte                         
00002201  convertAnalogToDigital               
0000226d  Key_1                                
000022d5  __cmpdf2                             
000022d5  __eqdf2                              
000022d5  __ledf2                              
000022d5  __ltdf2                              
000022d5  __nedf2                              
0000233d  __aeabi_dcmpeq                       
00002351  __aeabi_dcmplt                       
00002365  __aeabi_dcmple                       
00002379  __aeabi_dcmpge                       
0000238d  __aeabi_dcmpgt                       
000023a1  __aeabi_fcmpeq                       
000023b5  __aeabi_fcmplt                       
000023c9  __aeabi_fcmple                       
000023dd  __aeabi_fcmpge                       
000023f1  __aeabi_fcmpgt                       
00002405  OLED_Clear                           
00002465  DL_I2C_fillControllerTXFIFO          
000024c5  SYSCFG_DL_I2C_OLED_init              
0000251d  SYSCFG_DL_UART_0_init                
000026a9  Track_Init                           
00002741  __aeabi_d2iz                         
00002741  __fixdfsi                            
0000278b  adc_getValue                         
000027d5  DL_UART_init                         
0000281d  OLED_DisplayTurn                     
00002865  SYSCFG_DL_ADC12_0_init               
000028ad  SYSCFG_DL_SYSCTL_init                
000028f5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002939  Handle_Lost_Line                     
0000297d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000029c1  DL_ADC12_setClockConfig              
00002a01  Key                                  
00002a41  UART0_IRQHandler                     
00002a81  __aeabi_uidiv                        
00002a81  __aeabi_uidivmod                     
00002b39  Get_Anolog_Value                     
00002b75  Track_PID_Control                    
00002bb1  Track_Weighted_Control               
00002bed  __aeabi_i2f                          
00002bed  __floatsisf                          
00002c29  __gesf2                              
00002c29  __gtsf2                              
00002c65  __TI_auto_init_nobinit_nopinit       
00002ca1  __cmpsf2                             
00002ca1  __eqsf2                              
00002ca1  __lesf2                              
00002ca1  __ltsf2                              
00002ca1  __nesf2                              
00002cdd  __muldsi3                            
00002d19  __aeabi_f2iz                         
00002d19  __fixsfsi                            
00002d85  OLED_ColorTurn                       
00002db9  SYSCFG_DL_TIMER_0_init               
00002ded  SYSCFG_DL_init                       
00002e51  OLED_Pow                             
00002e81  SysTick_Handler                      
00002f09  __aeabi_i2d                          
00002f09  __floatsidf                          
00002f35  Calculate_Position_Error             
00003025  __aeabi_ui2f                         
00003025  __floatunsisf                        
0000304d  _c_int00_noargs                      
0000309b  DL_I2C_setClockConfig                
000030c1  __aeabi_ui2d                         
000030c1  __floatunsidf                        
00003125  delay_ms                             
00003299  DL_Timer_setCaptCompUpdateMethod     
000032b5  DL_Timer_setClockConfig              
000034fd  DL_Timer_setCaptureCompareOutCtl     
0000355d  Handle_Intersection                  
00003575  Way_With_Analog                      
000036d9  Key_Init_Debounce                    
000036ed  Left_Control                         
00003701  Left_Little_Control                  
00003715  Right_Control                        
00003729  Right_Little_Control                 
00003761  DL_UART_setClockConfig               
00003773  TI_memcpy_small                      
00003785  __TI_decompress_none                 
000037b9  DL_Timer_setCaptureCompareValue      
000037c9  Key_System_Tick_Inc                  
000037d9  __TI_zero_init                       
000037e9  Get_Digtal_For_User                  
000037f7  TI_memset_small                      
00003805  SYSCFG_DL_SYSTICK_init               
00003811  __aeabi_memclr                       
00003811  __aeabi_memclr4                      
00003811  __aeabi_memclr8                      
0000381d  get_systicks                         
00003829  scheduler_init                       
00003835  DL_Common_delayCycles                
00003841  __aeabi_memcpy                       
00003841  __aeabi_memcpy4                      
00003841  __aeabi_memcpy8                      
00003849  abort                                
0000384f  ADC0_IRQHandler                      
0000384f  ADC1_IRQHandler                      
0000384f  AES_IRQHandler                       
0000384f  CANFD0_IRQHandler                    
0000384f  DAC0_IRQHandler                      
0000384f  DMA_IRQHandler                       
0000384f  Default_Handler                      
0000384f  GROUP0_IRQHandler                    
0000384f  HardFault_Handler                    
0000384f  I2C0_IRQHandler                      
0000384f  I2C1_IRQHandler                      
0000384f  NMI_Handler                          
0000384f  PendSV_Handler                       
0000384f  RTC_IRQHandler                       
0000384f  SPI0_IRQHandler                      
0000384f  SPI1_IRQHandler                      
0000384f  SVC_Handler                          
0000384f  TIMA0_IRQHandler                     
0000384f  TIMA1_IRQHandler                     
0000384f  TIMG12_IRQHandler                    
0000384f  TIMG6_IRQHandler                     
0000384f  TIMG7_IRQHandler                     
0000384f  TIMG8_IRQHandler                     
0000384f  UART1_IRQHandler                     
0000384f  UART2_IRQHandler                     
0000384f  UART3_IRQHandler                     
00003852  C$$EXIT                              
00003853  HOSTexit                             
00003857  Reset_Handler                        
0000386d  _system_pre_init                     
00003870  asc2_2412                            
000045cc  asc2_1608                            
00004bbc  asc2_1206                            
00005030  asc2_0806                            
0000533c  __TI_Handler_Table_Base              
00005348  __TI_Handler_Table_Limit             
00005350  __TI_CINIT_Base                      
00005360  __TI_CINIT_Limit                     
00005360  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
202006e0  motor_pid                            
20200704  track_ctrl                           
20200724  Anolog                               
20200734  black                                
20200744  white                                
20200754  key1_ctrl                            
20200768  D_Num                                
2020076c  Run                                  
20200770  delay_times                          
20200778  uart_rx_index                        
20200779  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[235 symbols]
